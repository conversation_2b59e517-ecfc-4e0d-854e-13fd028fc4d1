package com.AAZl3l3.NettyWSServe;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;

@SpringBootApplication
@Import({
        // 添加请求头认证
        com.AAZl3l4.common.configuration.HeaderAuthenticationFilter.class,
        // 添加权限认证
        com.AAZl3l4.common.configuration.SecurityConfig.class,
        // 添加认证错误处理
        com.AAZl3l4.common.configuration.Nopermission.class,
        // 添加mybatisPlus配置
        com.AAZl3l4.common.configuration.MpConfig.class,
        // 添加mvc配置
        com.AAZl3l4.common.configuration.WebMvcConfig.class,
        // 添加全局异常处理
        com.AAZl3l4.common.configuration.GlobalExceptionHandler.class,
        // 添加feign客户端配置
        com.AAZl3l4.common.configuration.FeignApiConfig.class,
        // 添加feign fallback
        com.AAZl3l4.common.feignApi.UserServeApiFallbackFactory.class,
        // 添加异步线程池
        com.AAZl3l4.common.configuration.AsyncConfig.class,
        // 添加redis配置
        com.AAZl3l4.common.configuration.RedisConfig.class,

})
@EnableFeignClients(basePackages = "com.AAZl3l4.common.feignApi")
public class NettyApplication {

    public static void main(String[] args) {
        new SpringApplicationBuilder(NettyApplication.class).run(args);
    }
}

