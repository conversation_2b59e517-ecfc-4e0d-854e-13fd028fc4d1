package com.AAZl3l4.MallService.controller;

import com.AAZl3l4.MallService.pojo.CartItem;
import com.AAZl3l4.MallService.service.ICartItemService;
import com.AAZl3l4.common.utils.Result;
import com.AAZl3l4.common.utils.UserTool;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

@RestController
@RequestMapping("/cart")
public class CartController {

    @Autowired
    private ICartItemService cartItemService;

    @PostMapping("/add")
    public Result add(@RequestBody CartItem cartItem) {
        cartItem.setUserId(UserTool.getid());
        cartItem.setCreatedAt(LocalDateTime.now());
        return cartItemService.save(cartItem) ? Result.succeed("添加成功") : Result.error("添加失败");
    }

    @GetMapping("/list")
    public Result list() {
        Integer userId = UserTool.getid();
        QueryWrapper<CartItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return Result.succeed(cartItemService.list(queryWrapper));
    }

    @PostMapping("/delete/{id}")
    public Result delete(@PathVariable Integer id) {
        return cartItemService.removeById(id) ? Result.succeed("删除成功") : Result.error("删除失败");
    }
}
