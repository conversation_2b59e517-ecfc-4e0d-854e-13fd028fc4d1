---
title: 个人项目
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 个人项目

Base URLs:

# Authentication

# PullController

## GET 获取私聊聊天记录

GET /msg/single

获取私聊聊天记录

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|other|query|string| 否 |none|
|last|query|integer| 否 |none|
|size|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## GET 获取群聊聊天记录

GET /msg/group

获取群聊聊天记录

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|last|query|integer| 否 |none|
|size|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## GET 获取在线用户列表

GET /msg/list

获取在线用户列表

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

# sse服务

## GET 订阅sse连接

GET /sse/subscribe

订阅sse连接

> 返回示例

> 200 Response

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[SseEmitter](#schemasseemitter)|

## POST 发送消息

POST /sse/send

发送消息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|message|query|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

# 用户服务

## GET 返回用户列表

GET /list

返回用户列表

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "name": "",
      "password": "",
      "avatarUrl": "",
      "email": "",
      "age": 0,
      "sex": "",
      "roles": "",
      "isban": "",
      "createTime": "",
      "money": 0,
      "wxId": ""
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResultListUser](#schemaresultlistuser)|

## GET 查询默认地址

GET /getDefault

查询默认地址

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|userId|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## GET 通过id查询用户信息

GET /info

通过id查询用户信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "name": "",
  "password": "",
  "avatarUrl": "",
  "email": "",
  "age": 0,
  "sex": "",
  "roles": "",
  "isban": "",
  "createTime": "",
  "money": 0,
  "wxId": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[User](#schemauser)|

## POST 更新用户信息

POST /update

更新用户信息

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "password": "string",
  "avatarUrl": "string",
  "email": "<EMAIL>",
  "age": 0,
  "sex": "s",
  "roles": "string",
  "isban": "s",
  "createTime": "string",
  "money": 0,
  "wxId": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[User](#schemauser)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST login

POST /login

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|userid|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 用户注册

POST /register

用户注册

> Body 请求参数

```yaml
imgBase64: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|imgCode|query|string| 是 |none|
|emailCode|query|string| 是 |none|
|uuid|header|string| 是 |none|
|body|body|object| 否 |none|
|» imgBase64|body|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 修改头像

POST /updateAvatar

修改头像

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 更新用户人脸信息

POST /updateFace

更新用户人脸信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|imgBase64|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 申请修改角色

POST /uprole

申请修改角色

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|role|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

# 管理员服务

## GET 管理员根据id获取用户的全部信息

GET /admin/info

管理员根据id获取用户的全部信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|userid|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 管理员更新用户的信息

POST /admin/updata

管理员更新用户的信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 否 |用户id|
|name|query|string| 是 |用户名|
|password|query|string| 是 |密码|
|avatarUrl|query|string| 否 |头像|
|email|query|string(email)| 否 |邮箱|
|age|query|integer| 是 |年龄|
|sex|query|string| 是 |性别 男或女|
|roles|query|string| 是 |角色 ADMIN 管理员 USER用户 MERCHANT商家 UP UP主|
|isban|query|string| 是 |是否封禁 0:正常 1:禁止|
|createTime|query|string| 否 |创建时间|
|money|query|number| 否 |余额|
|wxId|query|string| 否 |微信id|

> 返回示例

> 200 Response

```json
{
  "code": null,
  "msg": null,
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 封禁/解封用户

POST /admin/ban

封禁/解封用户

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|userid|query|integer| 否 |none|
|status|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

# 支付宝付款服务

## GET 创建支付

GET /pay/create

创建支付

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|orderId|query|string| 是 |none|
|amount|query|number| 是 |none|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|string|

## GET 查询支付状态

GET /pay/status/{orderId}

查询支付状态

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|orderId|path|string| 是 |none|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|string|

## POST 异步通知处理

POST /pay/callback

异步通知处理

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|string|

## POST 退款接口

POST /pay/refund

退款接口

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|orderId|query|string| 是 |none|
|refundAmount|query|number| 是 |none|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|string|

## GET 下载对账单

GET /pay/download-bill

下载对账单

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|billDate|query|string| 是 |none|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|string|

# AddressController

## POST 添加地址

POST /address/add

添加地址

> Body 请求参数

```json
{
  "id": 0,
  "userId": 0,
  "phone": "string",
  "address": "string",
  "isDefault": "s"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[Address](#schemaaddress)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 修改地址

POST /address/update

修改地址

> Body 请求参数

```json
{
  "id": 0,
  "userId": 0,
  "phone": "string",
  "address": "string",
  "isDefault": "s"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[Address](#schemaaddress)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 删除地址

POST /address/delete

删除地址

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

# 微信登录服务类

## GET 微信验证

GET /wx/mp/callback

微信验证

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|signature|query|string| 是 |none|
|timestamp|query|string| 是 |none|
|nonce|query|string| 是 |none|
|echostr|query|string| 是 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 微信回调

POST /wx/mp/callback

微信回调

> Body 请求参数

```json
"string"
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|signature|query|string| 是 |none|
|timestamp|query|string| 是 |none|
|nonce|query|string| 是 |none|
|body|body|string| 否 |none|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|string|

## GET 生成绑定二维码

GET /wx/bind/qrcode

生成绑定二维码

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## GET 生成登录二维码

GET /wx/login/qrcode

生成登录二维码

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## GET 轮询拿 JWT

GET /wx/login/token

轮询拿 JWT

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|scene|query|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

# 验证码

## GET 获取图片验证码

GET /authcode/getimg

获取图片验证码

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 获取邮箱验证码

POST /authcode/getemail

获取邮箱验证码

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|email|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResultString](#schemaresultstring)|

# 角色审核服务

## GET 获取所有审核信息 支持分页和筛选

GET /roleReview/list

获取所有审核信息 支持分页和筛选

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNum|query|integer| 否 |none|
|pageSize|query|integer| 否 |none|
|status|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 审核用户角色

POST /roleReview/review

审核用户角色

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |none|
|status|query|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

# LikeController

## POST 添加/取消点赞点赞

POST /like/add

添加/取消点赞点赞

> Body 请求参数

```json
{
  "id": 0,
  "userId": 0,
  "newsId": 0,
  "likeTime": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[Like](#schemalike)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 分页查询当前用户所有点赞

POST /like/list

分页查询当前用户所有点赞

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|integer| 是 |none|
|size|query|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

# ArticleController

## POST 新增文章

POST /article/add

新增文章

> Body 请求参数

```json
{
  "articleId": 0,
  "authorId": 0,
  "categoryId": 0,
  "productId": 0,
  "location": {
    "lat": 0,
    "lon": 0
  },
  "title": "string",
  "content": "string",
  "isRecommended": false,
  "likeCount": 0,
  "createTime": "string",
  "updateTime": "string",
  "coverUrl": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[Article](#schemaarticle)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 删除文章

POST /article/delete/{id}

删除文章

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 更新文章

POST /article/update

更新文章

> Body 请求参数

```json
{
  "articleId": 0,
  "authorId": 0,
  "categoryId": 0,
  "productId": 0,
  "location": {
    "lat": 0,
    "lon": 0
  },
  "title": "string",
  "content": "string",
  "isRecommended": false,
  "likeCount": 0,
  "createTime": "string",
  "updateTime": "string",
  "coverUrl": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[Article](#schemaarticle)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": null,
  "msg": null,
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## GET 根据主键查询

GET /article/{id}

根据主键查询

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## GET 全文搜索文章（时间/分类/作者/关键词/距离/置顶/高亮/分页）

GET /article/search

全文搜索文章（时间/分类/作者/关键词/距离/置顶/高亮/分页）

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|keyword|query|string| 否 |全文关键词|
|categoryId|query|integer| 否 |分类|
|authorId|query|integer| 否 |作者|
|start|query|string| 否 |时间区间|
|end|query|string| 否 |none|
|lat|query|number| 否 |中心点坐标|
|lon|query|number| 否 |none|
|distance|query|string| 否 |距离表达式 如 "10km"|
|page|query|integer| 否 |分页|
|size|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

# NewsContentController

## GET 分页查询当前文章的所有评论

GET /content/list/{id}

分页查询当前文章的所有评论

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |none|
|page|query|integer| 是 |none|
|size|query|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 添加评论

POST /content/add

添加评论

> Body 请求参数

```json
{
  "id": 0,
  "content": "string",
  "createTime": "string",
  "userId": 0,
  "newsId": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[NewsContent](#schemanewscontent)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 删除评论

POST /content/delete/{id}

删除评论

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": null,
  "msg": null,
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 管理员删除评论

POST /content/addelete/{id}

管理员删除评论

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

# NewsCategoryController

## GET 查询所有分类

GET /category/list

查询所有分类

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## GET 添加分类

GET /category/add

添加分类

> Body 请求参数

```json
{
  "id": 0,
  "typeName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 否 |none|
|typeName|query|string| 否 |none|
|body|body|[NewsCategory](#schemanewscategory)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## GET 删除分类

GET /category/delete

删除分类

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

# OrderController

## POST 添加订单

POST /order/create

添加订单

> Body 请求参数

```json
{
  "orders": [
    {
      "id": 0,
      "orderId": 0,
      "productId": 0,
      "productName": "string",
      "productImage": "string",
      "unitPrice": 0,
      "quantity": 0,
      "amount": 0
    }
  ],
  "AddressId": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[OrderItemDTO](#schemaorderitemdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 取消订单

POST /order/cancel

取消订单

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|orderId|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 支付订单

POST /order/pay

支付订单

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|orderId|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": null,
  "msg": null,
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## GET 查询本人订单

GET /order/list

查询本人订单

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 0,
      "status": "",
      "amount": 0,
      "userId": 0,
      "userAddress": "",
      "createTime": "",
      "updateTime": "",
      "userPhone": ""
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResultListOrder](#schemaresultlistorder)|

## GET 查询订单详细

GET /order/info

查询订单详细

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": 0,
    "status": "",
    "amount": 0,
    "userId": 0,
    "userAddress": "",
    "createTime": "",
    "updateTime": "",
    "userPhone": ""
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResultOrder](#schemaresultorder)|

## GET 导出报表

GET /order/report

导出报表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|productId|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# CommentController

## GET 分页查询当前商品的所有评论

GET /comment/list/{id}

分页查询当前商品的所有评论

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |none|
|page|query|integer| 是 |none|
|size|query|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 添加评论

POST /comment/add

添加评论

> Body 请求参数

```json
{
  "id": 0,
  "productId": 0,
  "creationTime": "string",
  "content": "string",
  "userid": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[Comment](#schemacomment)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 删除评论

POST /comment/delete/{id}

删除评论

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": null,
  "msg": null,
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 管理员删除评论

POST /comment/addelete/{id}

管理员删除评论

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

# ProductController

## POST 新增

POST /product/add

新增

> Body 请求参数

```json
{
  "productId": 0,
  "MerchantId": 0,
  "name": "string",
  "price": 0,
  "stock": 0,
  "categoryId": 0,
  "introduction": "string",
  "onShelfTime": "string",
  "imageUrl": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[Product](#schemaproduct)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 根据主键删除

POST /product/delete/{id}

根据主键删除

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": null,
  "msg": null,
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## POST 根据主键更新

POST /product/update

根据主键更新

> Body 请求参数

```json
{
  "productId": 0,
  "MerchantId": 0,
  "name": "string",
  "price": 0,
  "stock": 0,
  "categoryId": 0,
  "introduction": "string",
  "onShelfTime": "string",
  "imageUrl": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[Product](#schemaproduct)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## GET 根据主键查询

GET /product/{id}

根据主键查询

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

## GET 分页/高亮/分类/价格区间查询 支持条件拼接 包括查看全部商品

GET /product/search

分页/高亮/分类/价格区间查询 支持条件拼接 包括查看全部商品

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|category|query|string| 否 |none|
|min|query|number| 否 |none|
|max|query|number| 否 |none|
|key|query|string| 否 |none|
|page|query|integer| 是 |none|
|size|query|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[Result](#schemaresult)|

# 文件服务

## POST 上传文件

POST /upload

上传文件

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |none|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|string|

# 数据模型

<h2 id="tocS_Pet">Pet</h2>

<a id="schemapet"></a>
<a id="schema_Pet"></a>
<a id="tocSpet"></a>
<a id="tocspet"></a>

```json
{
  "id": 1,
  "category": {
    "id": 1,
    "name": "string"
  },
  "name": "doggie",
  "photoUrls": [
    "string"
  ],
  "tags": [
    {
      "id": 1,
      "name": "string"
    }
  ],
  "status": "available"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||宠物ID编号|
|category|[Category](#schemacategory)|true|none||分组|
|name|string|true|none||名称|
|photoUrls|[string]|true|none||照片URL|
|tags|[[Tag](#schematag)]|true|none||标签|
|status|string|true|none||宠物销售状态|

#### 枚举值

|属性|值|
|---|---|
|status|available|
|status|pending|
|status|sold|

<h2 id="tocS_Category">Category</h2>

<a id="schemacategory"></a>
<a id="schema_Category"></a>
<a id="tocScategory"></a>
<a id="tocscategory"></a>

```json
{
  "id": 1,
  "name": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||分组ID编号|
|name|string|false|none||分组名称|

<h2 id="tocS_Tag">Tag</h2>

<a id="schematag"></a>
<a id="schema_Tag"></a>
<a id="tocStag"></a>
<a id="tocstag"></a>

```json
{
  "id": 1,
  "name": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||标签ID编号|
|name|string|false|none||标签名称|

<h2 id="tocS_Object">Object</h2>

<a id="schemaobject"></a>
<a id="schema_Object"></a>
<a id="tocSobject"></a>
<a id="tocsobject"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_Result">Result</h2>

<a id="schemaresult"></a>
<a id="schema_Result"></a>
<a id="tocSresult"></a>
<a id="tocsresult"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|data|object|false|none||none|

<h2 id="tocS_Handler">Handler</h2>

<a id="schemahandler"></a>
<a id="schema_Handler"></a>
<a id="tocShandler"></a>
<a id="tocshandler"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_news">news</h2>

<a id="schemanews"></a>
<a id="schema_news"></a>
<a id="tocSnews"></a>
<a id="tocsnews"></a>

```json
{
  "id": 0,
  "title": "string",
  "content": "string",
  "typeId": "string",
  "createTime": "string",
  "userId": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|false|none||none|
|title|string|true|none||none|
|content|string|true|none||none|
|typeId|string|true|none||none|
|createTime|string|false|none||none|
|userId|integer|false|none||none|

<h2 id="tocS_MapString">MapString</h2>

<a id="schemamapstring"></a>
<a id="schema_MapString"></a>
<a id="tocSmapstring"></a>
<a id="tocsmapstring"></a>

```json
{
  "key": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|string|false|none||none|

<h2 id="tocS_type">type</h2>

<a id="schematype"></a>
<a id="schema_type"></a>
<a id="tocStype"></a>
<a id="tocstype"></a>

```json
{
  "id": 0,
  "typeName": "string",
  "userId": 0,
  "createTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|false|none||none|
|typeName|string|true|none||none|
|userId|integer|false|none||none|
|createTime|string|false|none||none|

<h2 id="tocS_MediaType">MediaType</h2>

<a id="schemamediatype"></a>
<a id="schema_MediaType"></a>
<a id="tocSmediatype"></a>
<a id="tocsmediatype"></a>

```json
{
  "type": "string",
  "subtype": "string",
  "parameters": {
    "key": "string"
  },
  "toStringValue": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|type|string|false|none||none|
|subtype|string|false|none||none|
|parameters|[MapString](#schemamapstring)|false|none||none|
|toStringValue|string¦null|false|none||none|

<h2 id="tocS_list">list</h2>

<a id="schemalist"></a>
<a id="schema_list"></a>
<a id="tocSlist"></a>
<a id="tocslist"></a>

```json
{
  "id": 0,
  "liked": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|false|none||none|
|liked|boolean|false|none||none|

<h2 id="tocS_DataWithMediaType">DataWithMediaType</h2>

<a id="schemadatawithmediatype"></a>
<a id="schema_DataWithMediaType"></a>
<a id="tocSdatawithmediatype"></a>
<a id="tocsdatawithmediatype"></a>

```json
{
  "data": {},
  "mediaType": {
    "type": "string",
    "subtype": "string",
    "parameters": {
      "key": "string"
    },
    "toStringValue": "string"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|data|object|false|none||none|
|mediaType|[MediaType](#schemamediatype)|false|none||none|

<h2 id="tocS_comments">comments</h2>

<a id="schemacomments"></a>
<a id="schema_comments"></a>
<a id="tocScomments"></a>
<a id="tocscomments"></a>

```json
{
  "id": 0,
  "userId": "string",
  "newsId": 0,
  "text": "string",
  "time": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|false|none||none|
|userId|string|false|none||none|
|newsId|integer|true|none||none|
|text|string|true|none||none|
|time|string|false|none||none|

<h2 id="tocS_Throwable">Throwable</h2>

<a id="schemathrowable"></a>
<a id="schema_Throwable"></a>
<a id="tocSthrowable"></a>
<a id="tocsthrowable"></a>

```json
{
  "detailMessage": "string",
  "cause": {
    "detailMessage": "string",
    "cause": {
      "detailMessage": "string",
      "cause": {
        "detailMessage": "string",
        "cause": {
          "detailMessage": null,
          "cause": null,
          "stackTrace": null,
          "suppressedExceptions": null
        },
        "stackTrace": [
          {}
        ],
        "suppressedExceptions": [
          {}
        ]
      },
      "stackTrace": [
        {
          "classLoaderName": "string",
          "moduleName": "string",
          "moduleVersion": "string",
          "declaringClass": "string",
          "methodName": "string",
          "fileName": "string",
          "lineNumber": 0,
          "format": -127
        }
      ],
      "suppressedExceptions": [
        {
          "detailMessage": "string",
          "cause": {},
          "stackTrace": [
            null
          ],
          "suppressedExceptions": [
            null
          ]
        }
      ]
    },
    "stackTrace": [
      {
        "classLoaderName": "string",
        "moduleName": "string",
        "moduleVersion": "string",
        "declaringClass": "string",
        "methodName": "string",
        "fileName": "string",
        "lineNumber": 0,
        "format": -127
      }
    ],
    "suppressedExceptions": [
      {
        "detailMessage": "string",
        "cause": {
          "detailMessage": "string",
          "cause": {},
          "stackTrace": [
            null
          ],
          "suppressedExceptions": [
            null
          ]
        },
        "stackTrace": [
          {
            "classLoaderName": null,
            "moduleName": null,
            "moduleVersion": null,
            "declaringClass": null,
            "methodName": null,
            "fileName": null,
            "lineNumber": null,
            "format": null
          }
        ],
        "suppressedExceptions": [
          {
            "detailMessage": null,
            "cause": null,
            "stackTrace": null,
            "suppressedExceptions": null
          }
        ]
      }
    ]
  },
  "stackTrace": [
    {
      "classLoaderName": "string",
      "moduleName": "string",
      "moduleVersion": "string",
      "declaringClass": "string",
      "methodName": "string",
      "fileName": "string",
      "lineNumber": 0,
      "format": -127
    }
  ],
  "suppressedExceptions": [
    {
      "detailMessage": "string",
      "cause": {
        "detailMessage": "string",
        "cause": {
          "detailMessage": "string",
          "cause": {},
          "stackTrace": [
            null
          ],
          "suppressedExceptions": [
            null
          ]
        },
        "stackTrace": [
          {
            "classLoaderName": null,
            "moduleName": null,
            "moduleVersion": null,
            "declaringClass": null,
            "methodName": null,
            "fileName": null,
            "lineNumber": null,
            "format": null
          }
        ],
        "suppressedExceptions": [
          {
            "detailMessage": null,
            "cause": null,
            "stackTrace": null,
            "suppressedExceptions": null
          }
        ]
      },
      "stackTrace": [
        {
          "classLoaderName": "string",
          "moduleName": "string",
          "moduleVersion": "string",
          "declaringClass": "string",
          "methodName": "string",
          "fileName": "string",
          "lineNumber": 0,
          "format": -127
        }
      ],
      "suppressedExceptions": [
        {
          "detailMessage": "string",
          "cause": {
            "detailMessage": null,
            "cause": null,
            "stackTrace": null,
            "suppressedExceptions": null
          },
          "stackTrace": [
            {}
          ],
          "suppressedExceptions": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|detailMessage|string|false|none||none|
|cause|[Throwable](#schemathrowable)|false|none||none|
|stackTrace|[[StackTraceElement](#schemastacktraceelement)]|false|none||none|
|suppressedExceptions|[[Throwable](#schemathrowable)]|false|none||none|

<h2 id="tocS_Users">Users</h2>

<a id="schemausers"></a>
<a id="schema_Users"></a>
<a id="tocSusers"></a>
<a id="tocsusers"></a>

```json
{
  "id": 0,
  "name": "string",
  "username": "string",
  "password": "string",
  "age": -127,
  "sex": -127,
  "tel": "string",
  "createTime": "string",
  "headImg": "string",
  "isban": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|false|none||none|
|name|string|true|none||none|
|username|string|true|none||none|
|password|string|true|none||none|
|age|integer|true|none||none|
|sex|integer|true|none||none|
|tel|string|true|none||none|
|createTime|string|false|none||none|
|headImg|string|false|none||none|
|isban|integer|false|none||none|

<h2 id="tocS_StackTraceElement">StackTraceElement</h2>

<a id="schemastacktraceelement"></a>
<a id="schema_StackTraceElement"></a>
<a id="tocSstacktraceelement"></a>
<a id="tocsstacktraceelement"></a>

```json
{
  "classLoaderName": "string",
  "moduleName": "string",
  "moduleVersion": "string",
  "declaringClass": "string",
  "methodName": "string",
  "fileName": "string",
  "lineNumber": 0,
  "format": -127
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|classLoaderName|string|false|none||none|
|moduleName|string|false|none||none|
|moduleVersion|string|false|none||none|
|declaringClass|string|false|none||none|
|methodName|string|false|none||none|
|fileName|string|false|none||none|
|lineNumber|integer|false|none||none|
|format|integer|false|none||none|

<h2 id="tocS_Login">Login</h2>

<a id="schemalogin"></a>
<a id="schema_Login"></a>
<a id="tocSlogin"></a>
<a id="tocslogin"></a>

```json
{
  "username": "string",
  "password": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|username|string|true|none||none|
|password|string|true|none||none|

<h2 id="tocS_Runnable">Runnable</h2>

<a id="schemarunnable"></a>
<a id="schema_Runnable"></a>
<a id="tocSrunnable"></a>
<a id="tocsrunnable"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_DefaultCallback">DefaultCallback</h2>

<a id="schemadefaultcallback"></a>
<a id="schema_DefaultCallback"></a>
<a id="tocSdefaultcallback"></a>
<a id="tocsdefaultcallback"></a>

```json
{
  "delegates": [
    {}
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|delegates|[[Runnable](#schemarunnable)]|false|none||none|

<h2 id="tocS_ErrorCallback">ErrorCallback</h2>

<a id="schemaerrorcallback"></a>
<a id="schema_ErrorCallback"></a>
<a id="tocSerrorcallback"></a>
<a id="tocserrorcallback"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_SseEmitter">SseEmitter</h2>

<a id="schemasseemitter"></a>
<a id="schema_SseEmitter"></a>
<a id="tocSsseemitter"></a>
<a id="tocssseemitter"></a>

```json
{
  "timeout": 0,
  "handler": {},
  "earlySendAttempts": [
    {
      "data": {},
      "mediaType": {
        "type": "string",
        "subtype": "string",
        "parameters": {
          "key": "string"
        },
        "toStringValue": "string"
      }
    }
  ],
  "complete": true,
  "failure": {
    "detailMessage": "string",
    "cause": {
      "detailMessage": "string",
      "cause": {
        "detailMessage": "string",
        "cause": {
          "detailMessage": null,
          "cause": null,
          "stackTrace": null,
          "suppressedExceptions": null
        },
        "stackTrace": [
          {}
        ],
        "suppressedExceptions": [
          {}
        ]
      },
      "stackTrace": [
        {
          "classLoaderName": "string",
          "moduleName": "string",
          "moduleVersion": "string",
          "declaringClass": "string",
          "methodName": "string",
          "fileName": "string",
          "lineNumber": 0,
          "format": -127
        }
      ],
      "suppressedExceptions": [
        {
          "detailMessage": "string",
          "cause": {},
          "stackTrace": [
            null
          ],
          "suppressedExceptions": [
            null
          ]
        }
      ]
    },
    "stackTrace": [
      {
        "classLoaderName": "string",
        "moduleName": "string",
        "moduleVersion": "string",
        "declaringClass": "string",
        "methodName": "string",
        "fileName": "string",
        "lineNumber": 0,
        "format": -127
      }
    ],
    "suppressedExceptions": [
      {
        "detailMessage": "string",
        "cause": {
          "detailMessage": "string",
          "cause": {},
          "stackTrace": [
            null
          ],
          "suppressedExceptions": [
            null
          ]
        },
        "stackTrace": [
          {
            "classLoaderName": null,
            "moduleName": null,
            "moduleVersion": null,
            "declaringClass": null,
            "methodName": null,
            "fileName": null,
            "lineNumber": null,
            "format": null
          }
        ],
        "suppressedExceptions": [
          {
            "detailMessage": null,
            "cause": null,
            "stackTrace": null,
            "suppressedExceptions": null
          }
        ]
      }
    ]
  },
  "timeoutCallback": {
    "delegates": [
      {}
    ]
  },
  "errorCallback": {},
  "completionCallback": {
    "delegates": [
      {}
    ]
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|timeout|integer(int64)¦null|false|none||none|
|handler|[Handler](#schemahandler)|false|none||none|
|earlySendAttempts|[[DataWithMediaType](#schemadatawithmediatype)]|false|none||none|
|complete|boolean|false|none||none|
|failure|[Throwable](#schemathrowable)|false|none||none|
|timeoutCallback|[DefaultCallback](#schemadefaultcallback)|false|none||none|
|errorCallback|[ErrorCallback](#schemaerrorcallback)|false|none||none|
|completionCallback|[DefaultCallback](#schemadefaultcallback)|false|none||none|

<h2 id="tocS_User">User</h2>

<a id="schemauser"></a>
<a id="schema_User"></a>
<a id="tocSuser"></a>
<a id="tocsuser"></a>

```json
{
  "id": 0,
  "name": "string",
  "password": "string",
  "avatarUrl": "string",
  "email": "<EMAIL>",
  "age": 0,
  "sex": "s",
  "roles": "string",
  "isban": "s",
  "createTime": "string",
  "money": 0,
  "wxId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|false|none||用户id|
|name|string|true|none||用户名|
|password|string|true|none||密码|
|avatarUrl|string|false|none||头像|
|email|string(email)|false|none||邮箱|
|age|integer|true|none||年龄|
|sex|string|true|none||性别 男或女|
|roles|string|true|none||角色 ADMIN 管理员 USER用户 MERCHANT商家 UP UP主|
|isban|string|true|none||是否封禁 0:正常 1:禁止|
|createTime|string|false|none||创建时间|
|money|number|false|none||余额|
|wxId|string|false|none||微信id|

<h2 id="tocS_ResultListUser">ResultListUser</h2>

<a id="schemaresultlistuser"></a>
<a id="schema_ResultListUser"></a>
<a id="tocSresultlistuser"></a>
<a id="tocsresultlistuser"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "name": "string",
      "password": "string",
      "avatarUrl": "string",
      "email": "<EMAIL>",
      "age": 0,
      "sex": "s",
      "roles": "string",
      "isban": "s",
      "createTime": "string",
      "money": 0,
      "wxId": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|data|[[User](#schemauser)]|false|none||none|

<h2 id="tocS_"></h2>

<a id="schema"></a>
<a id="schema_"></a>
<a id="tocS"></a>
<a id="tocs"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_Address">Address</h2>

<a id="schemaaddress"></a>
<a id="schema_Address"></a>
<a id="tocSaddress"></a>
<a id="tocsaddress"></a>

```json
{
  "id": 0,
  "userId": 0,
  "phone": "string",
  "address": "string",
  "isDefault": "s"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|false|none||none|
|userId|integer|true|none||none|
|phone|string|true|none||none|
|address|string|true|none||none|
|isDefault|string|true|none||0:不默认 1:默认|

<h2 id="tocS_ResultString">ResultString</h2>

<a id="schemaresultstring"></a>
<a id="schema_ResultString"></a>
<a id="tocSresultstring"></a>
<a id="tocsresultstring"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|data|string|false|none||none|

<h2 id="tocS_Like">Like</h2>

<a id="schemalike"></a>
<a id="schema_Like"></a>
<a id="tocSlike"></a>
<a id="tocslike"></a>

```json
{
  "id": 0,
  "userId": 0,
  "newsId": 0,
  "likeTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|false|none||none|
|userId|integer|true|none||none|
|newsId|integer(int64)|true|none||none|
|likeTime|string|false|none||none|

<h2 id="tocS_GeoPoint">GeoPoint</h2>

<a id="schemageopoint"></a>
<a id="schema_GeoPoint"></a>
<a id="tocSgeopoint"></a>
<a id="tocsgeopoint"></a>

```json
{
  "lat": 0,
  "lon": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|lat|number|false|none||none|
|lon|number|false|none||none|

<h2 id="tocS_Article">Article</h2>

<a id="schemaarticle"></a>
<a id="schema_Article"></a>
<a id="tocSarticle"></a>
<a id="tocsarticle"></a>

```json
{
  "articleId": 0,
  "authorId": 0,
  "categoryId": 0,
  "productId": 0,
  "location": {
    "lat": 0,
    "lon": 0
  },
  "title": "string",
  "content": "string",
  "isRecommended": false,
  "likeCount": 0,
  "createTime": "string",
  "updateTime": "string",
  "coverUrl": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|articleId|integer(int64)|false|none||none|
|authorId|integer|false|none||none|
|categoryId|integer|false|none||none|
|productId|integer|false|none||none|
|location|[GeoPoint](#schemageopoint)|false|none||none|
|title|string|false|none||none|
|content|string|false|none||none|
|isRecommended|boolean|false|none||none|
|likeCount|integer(int64)|false|none||none|
|createTime|string|false|none||none|
|updateTime|string|false|none||none|
|coverUrl|string|false|none||none|

<h2 id="tocS_NewsContent">NewsContent</h2>

<a id="schemanewscontent"></a>
<a id="schema_NewsContent"></a>
<a id="tocSnewscontent"></a>
<a id="tocsnewscontent"></a>

```json
{
  "id": 0,
  "content": "string",
  "createTime": "string",
  "userId": 0,
  "newsId": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|false|none||none|
|content|string|true|none||none|
|createTime|string|false|none||none|
|userId|integer|true|none||none|
|newsId|integer|true|none||none|

<h2 id="tocS_NewsCategory">NewsCategory</h2>

<a id="schemanewscategory"></a>
<a id="schema_NewsCategory"></a>
<a id="tocSnewscategory"></a>
<a id="tocsnewscategory"></a>

```json
{
  "id": 0,
  "typeName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|false|none||none|
|typeName|string|true|none||none|

<h2 id="tocS_OrderItem">OrderItem</h2>

<a id="schemaorderitem"></a>
<a id="schema_OrderItem"></a>
<a id="tocSorderitem"></a>
<a id="tocsorderitem"></a>

```json
{
  "id": 0,
  "orderId": 0,
  "productId": 0,
  "productName": "string",
  "productImage": "string",
  "unitPrice": 0,
  "quantity": 0,
  "amount": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|false|none||none|
|orderId|integer|true|none||none|
|productId|integer(int64)|true|none||none|
|productName|string|true|none||none|
|productImage|string|true|none||none|
|unitPrice|number|true|none||none|
|quantity|integer|true|none||none|
|amount|number|true|none||none|

<h2 id="tocS_OrderItemDTO">OrderItemDTO</h2>

<a id="schemaorderitemdto"></a>
<a id="schema_OrderItemDTO"></a>
<a id="tocSorderitemdto"></a>
<a id="tocsorderitemdto"></a>

```json
{
  "orders": [
    {
      "id": 0,
      "orderId": 0,
      "productId": 0,
      "productName": "string",
      "productImage": "string",
      "unitPrice": 0,
      "quantity": 0,
      "amount": 0
    }
  ],
  "AddressId": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|orders|[[OrderItem](#schemaorderitem)]|false|none||none|
|AddressId|integer|false|none||none|

<h2 id="tocS_Order">Order</h2>

<a id="schemaorder"></a>
<a id="schema_Order"></a>
<a id="tocSorder"></a>
<a id="tocsorder"></a>

```json
{
  "id": 0,
  "status": "s",
  "amount": 0,
  "userId": 0,
  "userAddress": "string",
  "createTime": "string",
  "updateTime": "string",
  "userPhone": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|false|none||none|
|status|string|false|none||0:下单 1:支付 2:发货 3:收货 4:退货|
|amount|number|true|none||none|
|userId|integer|true|none||none|
|userAddress|string|true|none||none|
|createTime|string|false|none||none|
|updateTime|string|false|none||none|
|userPhone|string|true|none||none|

<h2 id="tocS_ResultListOrder">ResultListOrder</h2>

<a id="schemaresultlistorder"></a>
<a id="schema_ResultListOrder"></a>
<a id="tocSresultlistorder"></a>
<a id="tocsresultlistorder"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": 0,
      "status": "s",
      "amount": 0,
      "userId": 0,
      "userAddress": "string",
      "createTime": "string",
      "updateTime": "string",
      "userPhone": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|data|[[Order](#schemaorder)]|false|none||none|

<h2 id="tocS_ResultOrder">ResultOrder</h2>

<a id="schemaresultorder"></a>
<a id="schema_ResultOrder"></a>
<a id="tocSresultorder"></a>
<a id="tocsresultorder"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": 0,
    "status": "s",
    "amount": 0,
    "userId": 0,
    "userAddress": "string",
    "createTime": "string",
    "updateTime": "string",
    "userPhone": "string"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||none|
|msg|string|false|none||none|
|data|[Order](#schemaorder)|false|none||none|

<h2 id="tocS_Comment">Comment</h2>

<a id="schemacomment"></a>
<a id="schema_Comment"></a>
<a id="tocScomment"></a>
<a id="tocscomment"></a>

```json
{
  "id": 0,
  "productId": 0,
  "creationTime": "string",
  "content": "string",
  "userid": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|false|none||none|
|productId|integer|true|none||none|
|creationTime|string|false|none||none|
|content|string|true|none||none|
|userid|integer|false|none||none|

<h2 id="tocS_Product">Product</h2>

<a id="schemaproduct"></a>
<a id="schema_Product"></a>
<a id="tocSproduct"></a>
<a id="tocsproduct"></a>

```json
{
  "productId": 0,
  "MerchantId": 0,
  "name": "string",
  "price": 0,
  "stock": 0,
  "categoryId": 0,
  "introduction": "string",
  "onShelfTime": "string",
  "imageUrl": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|productId|integer(int64)|false|none||none|
|MerchantId|integer|false|none||none|
|name|string|false|none||none|
|price|number|false|none||none|
|stock|integer|false|none||none|
|categoryId|integer|false|none||none|
|introduction|string|false|none||none|
|onShelfTime|string|false|none||none|
|imageUrl|string|false|none||none|

