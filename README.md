# NewsProMax - 新闻电商综合平台

## 项目简介

NewsProMax 是一个基于微服务架构的新闻电商综合平台，整合了新闻资讯、电商平台和社交功能。该项目使用现代化的Java技术栈构建，采用Spring Cloud Alibaba生态体系，实现了高可用、可扩展的分布式系统架构。

## 技术架构

### 核心技术栈
- **Java 21** - 最新的Java LTS版本
- **Spring Boot 3.x** - 快速应用开发框架
- **Spring Cloud Alibaba** - 微服务解决方案
- **MySQL 8.0** - 关系型数据库
- **Redis** - 高性能缓存和会话存储
- **Elasticsearch** - 全文搜索和数据分析
- **RabbitMQ** - 消息队列服务
- **Nacos** - 服务注册发现和配置中心
- **Sentinel** - 流量控制和熔断降级
- **Seata** - 分布式事务解决方案
- **MinIO** - 对象存储服务
- **Netty** - 高性能网络通信框架
- **WebSocket** - 实时通信协议

### 服务模块

#### 1. Gateway (网关服务)
- 统一入口和路由转发
- JWT身份认证和权限校验
- 请求拦截和过滤
- 负载均衡

#### 2. UserServe (用户服务)
- 用户注册、登录和认证
- 微信扫码登录和绑定
- 角色管理和审核
- 支付宝支付集成
- 人脸验证功能
- 邮件通知服务
- SSE实时消息推送

#### 3. NewsServe (新闻服务)
- 新闻文章管理
- 全文搜索(支持高亮)
- 分类和标签管理
- 点赞和评论功能
- 地理位置搜索

#### 4. Mallserve (商城服务)
- 商品管理(ES搜索)
- 订单处理和支付
- 购物车功能
- 库存管理
- 报表导出(Excel)
- 延迟订单处理

#### 5. FileServe (文件服务)
- 文件上传和存储(MinIO)
- 图片处理和优化
- 文件访问控制

#### 6. NettyWSserve (WebSocket服务)
- 实时聊天功能
- 单聊和群聊
- 离线消息存储
- 延迟消息发送
- WebRTC信令服务

#### 7. Common (公共模块)
- 公共配置和工具类
- Feign客户端接口
- 统一异常处理
- 安全配置
- AOP日志记录

## 主要功能

### 用户系统
- 多种登录方式(账号密码、微信扫码)
- 角色权限管理(用户、UP主、商家、管理员)
- 个人信息管理
- 实名认证和人脸验证

### 新闻资讯
- 新闻发布和管理
- 多维度搜索(关键词、分类、时间、地理位置)
- 点赞和评论互动
- 内容推荐算法

### 电商平台
- 商品展示和搜索
- 购物车和订单管理
- 多种支付方式
- 库存和物流跟踪
- 商家后台管理

### 社交功能
- 实时聊天(单聊/群聊)
- 延迟消息发送
- 在线状态管理
- 好友系统

### 系统特性
- 微服务架构，高可用性
- 分布式事务一致性
- 消息队列异步处理
- 缓存优化提升性能
- 全文搜索功能
- 实时通信能力
- 完善的安全机制

### 环境要求
- JDK 21
- Maven 3.8+
- MySQL 8.0
- Redis
- Elasticsearch 8.6
- Sentinel
- seata
- RabbitMQ
- Nacos
- MinIO

### 启动步骤
1. 克隆项目代码
2. 初始化数据库
3. 配置各服务的application.yml
4. 启动基础设施服务(Nacos, Redis, MySQL等)
5. 依次启动各微服务
6. 访问网关端口开始使用

## 项目亮点

1. **完整的微服务生态**: 集成Spring Cloud Alibaba全套解决方案
2. **高并发设计**: 使用Netty实现高性能WebSocket通信
3. **搜索优化**: Elasticsearch实现复杂搜索和高亮显示
4. **分布式事务**: Seata保证数据一致性
5. **实时通信**: WebSocket+RabbitMQ实现消息实时推送
6. **安全机制**: JWT+Spring Security实现完善权限控制
7. **异步处理**: 消息队列解耦服务间调用
8. **监控治理**: Sentinel实现流量控制和熔断降级

## 开发团队

- 架构设计: AAZl3l4
- 后端开发: AAZl3l4

---

*注: 本项目为本人演示用途，实际部署需要根据具体需求进行调整，该项目的前端已经简单的完成了一部分以作dmeo，由于并不是项目重点，有需要可以联系本人获取项目截图。*
